{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_e76d35f6._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_4c46a49c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "JZiPvQ5XzRHs0H9v2y0AQPgZIzl0VWNXe/d02EC1/6o=", "__NEXT_PREVIEW_MODE_ID": "45d336a4d3e207a6ce41458389dc111e", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "c5767df99b093ff08e2d4671f46b61d9b5663de05da78224785380c882eb6b6b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "022ee02d9b835514a5ff81a81dba4bc93ade0be2756f851501fce1711e46e039"}}}, "instrumentation": null, "functions": {}}